<svg viewBox="0 0 1000 800" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="gradient1" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#4F46E5;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#7C3AED;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="gradient2" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#059669;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#0D9488;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="gradient3" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#DC2626;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#EA580C;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="gradient4" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#7C2D12;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#A16207;stop-opacity:1" />
    </linearGradient>
    <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
      <feDropShadow dx="2" dy="2" stdDeviation="3" flood-opacity="0.3"/>
    </filter>
  </defs>
  
  <!-- Background -->
  <rect width="1000" height="800" fill="#f8fafc"/>
  
  <!-- Title -->
  <text x="500" y="30" text-anchor="middle" font-family="Arial, sans-serif" font-size="24" font-weight="bold" fill="#1e293b">
    On-Device RAG Architecture - React Native
  </text>
  
  <!-- User Input -->
  <rect x="50" y="80" width="120" height="60" rx="8" fill="url(#gradient1)" filter="url(#shadow)"/>
  <text x="110" y="105" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="white">User Query</text>
  <text x="110" y="120" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="white">"What is...?"</text>
  
  <!-- Step 1: Query Encoding -->
  <rect x="220" y="60" width="160" height="100" rx="8" fill="url(#gradient2)" filter="url(#shadow)"/>
  <text x="300" y="85" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="white">1. Query Encoding</text>
  <text x="300" y="105" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="white">ONNX Embedding Model</text>
  <text x="300" y="120" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="white">(MiniLM/Instructor)</text>
  <text x="300" y="135" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="white">→ 384D Vector</text>
  
  <!-- Step 2: Vector Retrieval -->
  <rect x="420" y="60" width="160" height="100" rx="8" fill="url(#gradient3)" filter="url(#shadow)"/>
  <text x="500" y="85" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="white">2. Vector Retrieval</text>
  <text x="500" y="105" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="white">Rust HNSW Index</text>
  <text x="500" y="120" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="white">Approximate NN Search</text>
  <text x="500" y="135" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="white">→ Top-k Documents</text>
  
  <!-- Knowledge Base -->
  <rect x="420" y="200" width="160" height="80" rx="8" fill="#64748b" filter="url(#shadow)"/>
  <text x="500" y="225" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="white">Knowledge Base</text>
  <text x="500" y="245" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="white">Pre-indexed Documents</text>
  <text x="500" y="260" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="white">Memory-mapped Files</text>
  
  <!-- Step 3: Prompt Augmentation -->
  <rect x="620" y="60" width="160" height="100" rx="8" fill="url(#gradient4)" filter="url(#shadow)"/>
  <text x="700" y="85" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="white">3. Prompt Aug.</text>
  <text x="700" y="105" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="white">Context Assembly</text>
  <text x="700" y="120" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="white">Query + Retrieved Docs</text>
  <text x="700" y="135" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="white">→ Augmented Prompt</text>
  
  <!-- Step 4: LLM Inference -->
  <rect x="420" y="350" width="160" height="120" rx="8" fill="url(#gradient1)" filter="url(#shadow)"/>
  <text x="500" y="375" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="white">4. LLM Inference</text>
  <text x="500" y="395" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="white">llama.rn</text>
  <text x="500" y="410" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="white">7B Quantized Model</text>
  <text x="500" y="425" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="white">(4-bit/5-bit GGUF)</text>
  <text x="500" y="440" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="white">llama.cpp backend</text>
  <text x="500" y="455" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="white">→ Generated Answer</text>
  
  <!-- Output -->
  <rect x="220" y="520" width="160" height="80" rx="8" fill="url(#gradient2)" filter="url(#shadow)"/>
  <text x="300" y="545" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="white">Streaming Output</text>
  <text x="300" y="565" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="white">Incremental Display</text>
  <text x="300" y="580" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="white">Responsive UI</text>
  
  <!-- Arrows -->
  <!-- User to Encoding -->
  <path d="M170 110 L220 110" stroke="#374151" stroke-width="2" fill="none" marker-end="url(#arrowhead)"/>
  
  <!-- Encoding to Retrieval -->
  <path d="M380 110 L420 110" stroke="#374151" stroke-width="2" fill="none" marker-end="url(#arrowhead)"/>
  
  <!-- Retrieval to Knowledge Base -->
  <path d="M500 160 L500 200" stroke="#374151" stroke-width="2" fill="none" marker-end="url(#arrowhead)"/>
  
  <!-- Knowledge Base to Retrieval -->
  <path d="M480 200 L480 160" stroke="#374151" stroke-width="2" fill="none" marker-end="url(#arrowhead)"/>
  
  <!-- Retrieval to Augmentation -->
  <path d="M580 110 L620 110" stroke="#374151" stroke-width="2" fill="none" marker-end="url(#arrowhead)"/>
  
  <!-- Augmentation to LLM -->
  <path d="M700 160 L700 320 L580 320 L580 350" stroke="#374151" stroke-width="2" fill="none" marker-end="url(#arrowhead)"/>
  
  <!-- LLM to Output -->
  <path d="M420 410 L380 410 L380 560" stroke="#374151" stroke-width="2" fill="none" marker-end="url(#arrowhead)"/>
  
  <!-- Technical Details Box -->
  <rect x="750" y="300" width="220" height="320" rx="8" fill="#f1f5f9" stroke="#cbd5e1" stroke-width="1" filter="url(#shadow)"/>
  <text x="860" y="325" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#1e293b">Technical Stack</text>
  
  <text x="760" y="350" font-family="Arial, sans-serif" font-size="11" font-weight="bold" fill="#374151">Embedding:</text>
  <text x="760" y="365" font-family="Arial, sans-serif" font-size="10" fill="#64748b">• ONNX Runtime React Native</text>
  <text x="760" y="380" font-family="Arial, sans-serif" font-size="10" fill="#64748b">• MiniLM/Instructor models</text>
  
  <text x="760" y="405" font-family="Arial, sans-serif" font-size="11" font-weight="bold" fill="#374151">Retrieval:</text>
  <text x="760" y="420" font-family="Arial, sans-serif" font-size="10" fill="#64748b">• Rust HNSW implementation</text>
  <text x="760" y="435" font-family="Arial, sans-serif" font-size="10" fill="#64748b">• Native module compilation</text>
  
  <text x="760" y="460" font-family="Arial, sans-serif" font-size="11" font-weight="bold" fill="#374151">LLM:</text>
  <text x="760" y="475" font-family="Arial, sans-serif" font-size="10" fill="#64748b">• llama.rn (React Native)</text>
  <text x="760" y="490" font-family="Arial, sans-serif" font-size="10" fill="#64748b">• llama.cpp backend</text>
  <text x="760" y="505" font-family="Arial, sans-serif" font-size="10" fill="#64748b">• 7B parameter models</text>
  <text x="760" y="520" font-family="Arial, sans-serif" font-size="10" fill="#64748b">• 4-bit/5-bit quantization</text>
  
  <text x="760" y="545" font-family="Arial, sans-serif" font-size="11" font-weight="bold" fill="#374151">Optimizations:</text>
  <text x="760" y="560" font-family="Arial, sans-serif" font-size="10" fill="#64748b">• Asynchronous pipeline</text>
  <text x="760" y="575" font-family="Arial, sans-serif" font-size="10" fill="#64748b">• Memory-mapped files</text>
  <text x="760" y="590" font-family="Arial, sans-serif" font-size="10" fill="#64748b">• Model hot-swapping</text>
  <text x="760" y="605" font-family="Arial, sans-serif" font-size="10" fill="#64748b">• Multi-threading</text>
  
  <!-- Memory Management Box -->
  <rect x="50" y="300" width="180" height="180" rx="8" fill="#fef3c7" stroke="#f59e0b" stroke-width="1" filter="url(#shadow)"/>
  <text x="140" y="325" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#92400e">Memory Management</text>
  
  <text x="60" y="350" font-family="Arial, sans-serif" font-size="10" fill="#92400e">• Separate memory sessions</text>
  <text x="60" y="365" font-family="Arial, sans-serif" font-size="10" fill="#92400e">• ONNX model unloading</text>
  <text x="60" y="380" font-family="Arial, sans-serif" font-size="10" fill="#92400e">• LLM hot-swapping</text>
  <text x="60" y="395" font-family="Arial, sans-serif" font-size="10" fill="#92400e">• Memory-mapped indices</text>
  <text x="60" y="410" font-family="Arial, sans-serif" font-size="10" fill="#92400e">• Quantized weights</text>
  <text x="60" y="425" font-family="Arial, sans-serif" font-size="10" fill="#92400e">• Background threading</text>
  <text x="60" y="440" font-family="Arial, sans-serif" font-size="10" fill="#92400e">• Async orchestration</text>
  <text x="60" y="455" font-family="Arial, sans-serif" font-size="10" fill="#92400e">• UI thread separation</text>
  
  <!-- Performance Metrics -->
  <rect x="50" y="650" width="900" height="120" rx="8" fill="#e0f2fe" stroke="#0284c7" stroke-width="1" filter="url(#shadow)"/>
  <text x="500" y="675" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#0c4a6e">Performance Characteristics</text>
  
  <text x="70" y="700" font-family="Arial, sans-serif" font-size="11" font-weight="bold" fill="#0c4a6e">Context Length:</text>
  <text x="70" y="715" font-family="Arial, sans-serif" font-size="10" fill="#075985">4K tokens (sufficient for few docs)</text>
  
  <text x="270" y="700" font-family="Arial, sans-serif" font-size="11" font-weight="bold" fill="#0c4a6e">Memory Usage:</text>
  <text x="270" y="715" font-family="Arial, sans-serif" font-size="10" fill="#075985">Few GB RAM for 7B model</text>
  
  <text x="470" y="700" font-family="Arial, sans-serif" font-size="11" font-weight="bold" fill="#0c4a6e">Latency:</text>
  <text x="470" y="715" font-family="Arial, sans-serif" font-size="10" fill="#075985">TTFT + streaming TPS</text>
  
  <text x="620" y="700" font-family="Arial, sans-serif" font-size="11" font-weight="bold" fill="#0c4a6e">Architecture:</text>
  <text x="620" y="715" font-family="Arial, sans-serif" font-size="10" fill="#075985">Fully on-device, no cloud</text>
  
  <text x="770" y="700" font-family="Arial, sans-serif" font-size="11" font-weight="bold" fill="#0c4a6e">Embedding Dim:</text>
  <text x="770" y="715" font-family="Arial, sans-serif" font-size="10" fill="#075985">384 dimensions</text>
  
  <text x="70" y="740" font-family="Arial, sans-serif" font-size="11" font-weight="bold" fill="#0c4a6e">Model Format:</text>
  <text x="70" y="755" font-family="Arial, sans-serif" font-size="10" fill="#075985">GGUF quantized weights</text>
  
  <text x="270" y="740" font-family="Arial, sans-serif" font-size="11" font-weight="bold" fill="#0c4a6e">Search Algorithm:</text>
  <text x="270" y="755" font-family="Arial, sans-serif" font-size="10" fill="#075985">HNSW approximate NN</text>
  
  <text x="470" y="740" font-family="Arial, sans-serif" font-size="11" font-weight="bold" fill="#0c4a6e">Threading:</text>
  <text x="470" y="755" font-family="Arial, sans-serif" font-size="10" fill="#075985">Multi-threaded C++ backend</text>
  
  <text x="620" y="740" font-family="Arial, sans-serif" font-size="11" font-weight="bold" fill="#0c4a6e">UI Experience:</text>
  <text x="620" y="755" font-family="Arial, sans-serif" font-size="10" fill="#075985">Cloud-like streaming</text>
  
  <!-- Arrow marker definition -->
  <defs>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" 
            refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#374151"/>
    </marker>
  </defs>
  
  <!-- Flow Labels -->
  <text x="195" y="105" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="#6b7280">Text</text>
  <text x="400" y="105" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="#6b7280">Vector</text>
  <text x="600" y="105" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="#6b7280">Docs</text>
  <text x="650" y="240" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="#6b7280">Prompt</text>
  <text x="350" y="440" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="#6b7280">Answer</text>
</svg>