``` mermaid
graph TB
    %% React Native Layer
    subgraph "📱 React Native JavaScript"
        UI[User Interface<br/>Chat & Controls]
        RAGService[RAG Service<br/>Document Retrieval]
        ModelMgmt[Model Management<br/>Download & Loading]
    end
    
    %% Bridge Layer
    subgraph "🌉 Native Bridges"
        HNSWBridge[HNSW Bridge<br/>Vector Search]
        LLaMABridge[LLaMA Bridge<br/>Text Generation]
        FSBridge[File System Bridge<br/>Asset Management]
    end
    
    %% Native iOS
    subgraph "🍎 iOS Native (Swift/Objective-C)"
        iOSHNSW[iOS HNSW Module]
        iOSAssets[iOS Asset Bundle]
        iOSLib[Static Library<br/>libhnsw_lib.a]
    end
    
    %% Native Android
    subgraph "🤖 Android Native (Java/Kotlin)"
        AndroidHNSW[Android HNSW Module]
        AndroidAssets[Android Asset Bundle]
        AndroidLib[Shared Library<br/>libhnsw_native.so]
    end
    
    %% Rust Core
    subgraph "⚡ Rust Core"
        RustHNSW[hnsw_lib.rs<br/>Vector Search Engine]
    end
    
    %% Connections
    UI --> RAGService
    UI --> ModelMgmt
    
    RAGService --> HNSWBridge
    ModelMgmt --> LLaMABridge
    RAGService --> FSBridge
    
    HNSWBridge --> iOSHNSW
    HNSWBridge --> AndroidHNSW
    
    iOSHNSW --> iOSLib
    AndroidHNSW --> AndroidLib
    
    iOSLib --> RustHNSW
    AndroidLib --> RustHNSW
    
    FSBridge --> iOSAssets
    FSBridge --> AndroidAssets
    
    %% Styling
    classDef rn fill:#61dafb,stroke:#20232a,stroke-width:2px,color:#20232a
    classDef bridge fill:#ffd54f,stroke:#f57f17,stroke-width:2px
    classDef ios fill:#007aff,stroke:#0051d5,stroke-width:2px,color:white
    classDef android fill:#3ddc84,stroke:#00701a,stroke-width:2px
    classDef rust fill:#ce422b,stroke:#8b0000,stroke-width:2px,color:white
    
    class UI,RAGService,ModelMgmt rn
    class HNSWBridge,LLaMABridge,FSBridge bridge
    class iOSHNSW,iOSAssets,iOSLib ios
    class AndroidHNSW,AndroidAssets,AndroidLib android
    class RustHNSW rust
```